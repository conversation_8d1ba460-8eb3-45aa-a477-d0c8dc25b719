package com.BE.academicresource.service;

import com.BE.academicresource.config.SupabaseConfig;
import com.BE.academicresource.dto.FileUploadResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpDelete;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class SupabaseStorageService {
    
    private final SupabaseConfig supabaseConfig;
    
    public FileUploadResponseDTO uploadFile(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File is empty");
        }
        
        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        String uniqueFilename = UUID.randomUUID().toString() + fileExtension;
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String uploadUrl = supabaseConfig.getStorageUrl() + "/object/" + supabaseConfig.getBucketName() + "/" + uniqueFilename;
            
            HttpPost uploadRequest = new HttpPost(uploadUrl);
            uploadRequest.setHeader("Authorization", "Bearer " + supabaseConfig.getApiKey());
            uploadRequest.setHeader("Content-Type", file.getContentType());
            
            // Create multipart entity
            HttpEntity entity = MultipartEntityBuilder.create()
                    .addBinaryBody("file", file.getInputStream(), 
                                 ContentType.create(file.getContentType()), uniqueFilename)
                    .build();
            
            uploadRequest.setEntity(entity);
            
            try (ClassicHttpResponse response = httpClient.execute(uploadRequest)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                if (statusCode >= 200 && statusCode < 300) {
                    String publicUrl = supabaseConfig.getPublicUrl(uniqueFilename);
                    
                    return new FileUploadResponseDTO(
                        uniqueFilename,
                        publicUrl,
                        file.getContentType(),
                        file.getSize(),
                        "File uploaded successfully"
                    );
                } else {
                    log.error("Failed to upload file. Status: {}, Response: {}", statusCode, responseBody);
                    throw new RuntimeException("Failed to upload file to Supabase Storage");
                }
            }
        }
    }
    
    public boolean deleteFile(String fileName) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String deleteUrl = supabaseConfig.getStorageUrl() + "/object/" + supabaseConfig.getBucketName() + "/" + fileName;
            
            HttpDelete deleteRequest = new HttpDelete(deleteUrl);
            deleteRequest.setHeader("Authorization", "Bearer " + supabaseConfig.getApiKey());
            
            try (ClassicHttpResponse response = httpClient.execute(deleteRequest)) {
                int statusCode = response.getCode();
                
                if (statusCode >= 200 && statusCode < 300) {
                    log.info("File deleted successfully: {}", fileName);
                    return true;
                } else {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    log.error("Failed to delete file. Status: {}, Response: {}", statusCode, responseBody);
                    return false;
                }
            }
        } catch (IOException e) {
            log.error("Error deleting file: {}", fileName, e);
            return false;
        }
    }
    
    public String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        
        String publicUrlPrefix = supabaseConfig.getPublicUrl("");
        if (url.startsWith(publicUrlPrefix)) {
            return url.substring(publicUrlPrefix.length());
        }
        
        // If it's not a Supabase public URL, try to extract filename from the end
        String[] parts = url.split("/");
        return parts[parts.length - 1];
    }
}
