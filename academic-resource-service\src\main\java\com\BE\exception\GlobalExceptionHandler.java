package com.BE.exception;

import com.BE.model.response.DataResponseDTO;
import com.BE.utils.ResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        log.error("Resource not found: {}", ex.getMessage());
        return ResponseHandler.generateResponse(ex.getMessage(), HttpStatus.NOT_FOUND, null);
    }
    
    @ExceptionHandler(AcademicResourceException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleAcademicResourceException(AcademicResourceException ex) {
        log.error("Academic resource error: {}", ex.getMessage());
        return ResponseHandler.generateResponse(ex.getMessage(), HttpStatus.BAD_REQUEST, null);
    }
    
    @ExceptionHandler(FileUploadException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleFileUploadException(FileUploadException ex) {
        log.error("File upload error: {}", ex.getMessage());
        return ResponseHandler.generateResponse(ex.getMessage(), HttpStatus.BAD_REQUEST, null);
    }
    
    @ExceptionHandler(IOException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleIOException(IOException ex) {
        log.error("IO error: {}", ex.getMessage());
        return ResponseHandler.generateResponse("File operation failed: " + ex.getMessage(), 
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }
    
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException ex) {
        log.error("File size exceeded: {}", ex.getMessage());
        return ResponseHandler.generateResponse("File size exceeds maximum allowed limit", 
                HttpStatus.BAD_REQUEST, null);
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        log.error("Validation errors: {}", errors);
        return ResponseHandler.generateResponse("Validation failed", HttpStatus.BAD_REQUEST, errors);
    }
    
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.error("Invalid argument: {}", ex.getMessage());
        return ResponseHandler.generateResponse(ex.getMessage(), HttpStatus.BAD_REQUEST, null);
    }
    
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<DataResponseDTO<Object>> handleRuntimeException(RuntimeException ex) {
        log.error("Runtime error: {}", ex.getMessage(), ex);
        return ResponseHandler.generateResponse("An error occurred: " + ex.getMessage(), 
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<DataResponseDTO<Object>> handleGenericException(Exception ex) {
        log.error("Unexpected error: {}", ex.getMessage(), ex);
        return ResponseHandler.generateResponse("An unexpected error occurred", 
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }
}
