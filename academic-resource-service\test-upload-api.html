<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Academic Resource Upload API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Test Academic Resource Upload API</h1>
    
    <form id="uploadForm">
        <div class="form-group">
            <label for="type">Type:</label>
            <select id="type" name="type" required>
                <option value="">Select type</option>
                <option value="image">Image</option>
                <option value="gif">GIF</option>
                <option value="video">Video</option>
                <option value="webp">WebP</option>
                <option value="iframe">IFrame</option>
            </select>
        </div>

        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" required maxlength="255" 
                   placeholder="Enter resource name">
        </div>

        <div class="form-group">
            <label for="description">Description:</label>
            <textarea id="description" name="description" rows="3" 
                      placeholder="Enter resource description (optional)"></textarea>
        </div>

        <div class="form-group">
            <label for="tagIds">Tag IDs (comma separated):</label>
            <input type="text" id="tagIds" name="tagIds" 
                   placeholder="e.g., 1,2,3 (optional)">
        </div>

        <div class="form-group">
            <label for="file">File:</label>
            <input type="file" id="file" name="file" required>
        </div>

        <button type="submit">Upload Resource</button>
    </form>

    <div id="response"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const form = e.target;
            
            // Add form fields to FormData
            formData.append('type', form.type.value);
            formData.append('name', form.name.value);
            formData.append('description', form.description.value);
            
            // Handle tagIds - convert comma-separated string to array
            const tagIdsValue = form.tagIds.value.trim();
            if (tagIdsValue) {
                const tagIds = tagIdsValue.split(',').map(id => id.trim()).filter(id => id);
                tagIds.forEach(tagId => {
                    formData.append('tagIds', tagId);
                });
            }
            
            // Add file
            formData.append('file', form.file.files[0]);
            
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = 'Uploading...';
            
            try {
                const response = await fetch('http://localhost:8080/api/academic-resources/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.text();
                
                if (response.ok) {
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `Success!\n\n${result}`;
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.innerHTML = `Error (${response.status}):\n\n${result}`;
                }
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.innerHTML = `Network Error:\n\n${error.message}`;
            }
        });
    </script>
</body>
</html>
