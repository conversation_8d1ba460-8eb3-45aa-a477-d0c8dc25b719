package com.BE.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.multipart.MultipartFile;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AcademicResourceCreateWithFileRequest {

    @Schema(example = "image", description = "Resource type: image, gif, video, webp, iframe")
    @NotBlank(message = "Type is required")
    @Size(max = 20, message = "Type must not exceed 20 characters")
    String type;

    @Schema(example = "Math Formula Collection", description = "Resource name")
    @NotBlank(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    String name;

    @Schema(example = "Collection of important math formulas for grade 10", description = "Resource description")
    String description;

    @Schema(description = "File to upload")
    @NotNull(message = "File is required")
    MultipartFile file;

    @Schema(example = "[1, 2, 3]", description = "List of tag IDs to associate with this resource")
    Set<Long> tagIds;

    // Helper method to convert to AcademicResourceCreateRequest
    public AcademicResourceCreateRequest toCreateRequest() {
        AcademicResourceCreateRequest request = new AcademicResourceCreateRequest();
        request.setType(this.type);
        request.setName(this.name);
        request.setDescription(this.description);
        request.setTagIds(this.tagIds);
        // URL will be set after file upload
        return request;
    }
}
